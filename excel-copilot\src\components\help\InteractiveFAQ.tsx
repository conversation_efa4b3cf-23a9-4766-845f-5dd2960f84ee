'use client';

import { useState, useMemo } from 'react';
import { Search, ThumbsUp, ThumbsDown, MessageCircle, ChevronDown, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  helpful: number;
  notHelpful: number;
  lastUpdated: string;
  relatedQuestions?: string[];
}

interface InteractiveFAQProps {
  items?: FAQItem[];
  showSearch?: boolean;
  showCategories?: boolean;
  showVoting?: boolean;
  showRelated?: boolean;
  maxItems?: number;
  className?: string;
}

const DEFAULT_FAQ_ITEMS: FAQItem[] = [
  {
    id: '1',
    question: 'Como posso começar a usar o Excel Copilot?',
    answer: 'Para começar, faça login na plataforma e crie sua primeira planilha. Use o painel de chat à direita para dar comandos em linguagem natural como "Adicione uma coluna Total" ou "Calcule a média dos valores". O sistema entenderá suas instruções e executará automaticamente.',
    category: 'Iniciante',
    tags: ['tutorial', 'primeiros-passos', 'login'],
    helpful: 45,
    notHelpful: 3,
    lastUpdated: '2025-06-18',
    relatedQuestions: ['2', '3']
  },
  {
    id: '2',
    question: 'Quais formatos de arquivo são suportados?',
    answer: 'O Excel Copilot suporta os seguintes formatos: .xlsx (Excel moderno), .xls (Excel legado), .csv (valores separados por vírgula). Você pode importar arquivos existentes e exportar em qualquer um desses formatos.',
    category: 'Arquivos',
    tags: ['formatos', 'import', 'export', 'xlsx', 'csv'],
    helpful: 32,
    notHelpful: 1,
    lastUpdated: '2025-06-18',
    relatedQuestions: ['4', '5']
  },
  {
    id: '3',
    question: 'Como funciona a colaboração em tempo real?',
    answer: 'A colaboração permite que múltiplos usuários trabalhem na mesma planilha simultaneamente. As mudanças são sincronizadas automaticamente e você pode ver quem está editando em tempo real através de cursores coloridos. Para colaborar, compartilhe o link da planilha com outros usuários.',
    category: 'Colaboração',
    tags: ['colaboração', 'tempo-real', 'compartilhamento'],
    helpful: 28,
    notHelpful: 2,
    lastUpdated: '2025-06-17',
    relatedQuestions: ['6', '7']
  },
  {
    id: '4',
    question: 'Existe limite para o tamanho das planilhas?',
    answer: 'Os limites dependem do seu plano: Plano Gratuito (até 1.000 linhas), Plano Pro (até 10.000 linhas), Plano Enterprise (sem limite). O tamanho máximo de arquivo para upload é de 50MB.',
    category: 'Limites',
    tags: ['limites', 'planos', 'tamanho', 'linhas'],
    helpful: 19,
    notHelpful: 0,
    lastUpdated: '2025-06-18',
    relatedQuestions: ['8', '9']
  },
  {
    id: '5',
    question: 'Como posso exportar minha planilha?',
    answer: 'Use o botão "Exportar" na barra de ferramentas e escolha entre Excel (.xlsx) ou CSV (.csv). O download começará automaticamente. Você também pode usar comandos de IA como "Exporte esta planilha como CSV".',
    category: 'Exportação',
    tags: ['exportar', 'download', 'xlsx', 'csv'],
    helpful: 41,
    notHelpful: 1,
    lastUpdated: '2025-06-18',
    relatedQuestions: ['2', '10']
  },
  {
    id: '6',
    question: 'Como compartilhar uma planilha com outros usuários?',
    answer: 'Clique no botão "Compartilhar" no canto superior direito da planilha. Você pode gerar um link de compartilhamento ou convidar usuários por email. Defina as permissões (visualizar, editar ou comentar) para cada usuário.',
    category: 'Colaboração',
    tags: ['compartilhar', 'permissões', 'convite'],
    helpful: 35,
    notHelpful: 2,
    lastUpdated: '2025-06-17',
    relatedQuestions: ['3', '7']
  },
  {
    id: '7',
    question: 'Posso trabalhar offline?',
    answer: 'Atualmente, o Excel Copilot requer conexão com a internet para funcionar, pois utiliza IA em nuvem e sincronização em tempo real. Estamos trabalhando em uma versão offline para funcionalidades básicas.',
    category: 'Funcionalidades',
    tags: ['offline', 'internet', 'sincronização'],
    helpful: 15,
    notHelpful: 8,
    lastUpdated: '2025-06-16',
    relatedQuestions: ['3', '11']
  },
  {
    id: '8',
    question: 'Como funciona a cobrança dos planos?',
    answer: 'A cobrança é mensal ou anual. O plano gratuito não tem cobrança. Os planos pagos são cobrados automaticamente no cartão cadastrado. Você pode cancelar a qualquer momento e continuar usando até o final do período pago.',
    category: 'Pagamento',
    tags: ['cobrança', 'planos', 'pagamento', 'cancelamento'],
    helpful: 22,
    notHelpful: 1,
    lastUpdated: '2025-06-18',
    relatedQuestions: ['4', '12']
  },
  {
    id: '9',
    question: 'Quais comandos de IA posso usar?',
    answer: 'Você pode usar comandos como: "Calcule a soma da coluna A", "Crie um gráfico de barras", "Ordene por data", "Adicione uma coluna de percentual", "Destaque valores acima da média", "Crie uma tabela dinâmica". O sistema entende linguagem natural.',
    category: 'IA',
    tags: ['comandos', 'ia', 'linguagem-natural', 'exemplos'],
    helpful: 67,
    notHelpful: 2,
    lastUpdated: '2025-06-18',
    relatedQuestions: ['1', '13']
  },
  {
    id: '10',
    question: 'Como importar dados de outras fontes?',
    answer: 'Além de arquivos Excel e CSV, você pode conectar APIs externas, bancos de dados e serviços como Google Sheets. Use o menu "Importar" e selecione a fonte desejada. Algumas integrações requerem autenticação.',
    category: 'Integração',
    tags: ['importar', 'api', 'banco-dados', 'google-sheets'],
    helpful: 29,
    notHelpful: 3,
    lastUpdated: '2025-06-17',
    relatedQuestions: ['2', '14']
  }
];

export function InteractiveFAQ({
  items = DEFAULT_FAQ_ITEMS,
  showSearch = true,
  showCategories = true,
  showVoting = true,
  showRelated = true,
  maxItems,
  className = ''
}: InteractiveFAQProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('helpful');
  const [votedItems, setVotedItems] = useState<Record<string, 'up' | 'down'>>({});

  const categories = useMemo(() => {
    const cats = Array.from(new Set(items.map(item => item.category)));
    return ['all', ...cats];
  }, [items]);

  const filteredAndSortedItems = useMemo(() => {
    let filtered = items.filter(item => {
      const matchesSearch = searchQuery === '' || 
        item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
      
      return matchesSearch && matchesCategory;
    });

    // Ordenação
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'helpful':
          return (b.helpful - b.notHelpful) - (a.helpful - a.notHelpful);
        case 'recent':
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
        case 'alphabetical':
          return a.question.localeCompare(b.question);
        default:
          return 0;
      }
    });

    return maxItems ? filtered.slice(0, maxItems) : filtered;
  }, [items, searchQuery, selectedCategory, sortBy, maxItems]);

  const handleVote = (itemId: string, type: 'up' | 'down') => {
    if (votedItems[itemId]) {
      toast.error('Você já votou nesta pergunta');
      return;
    }

    setVotedItems(prev => ({ ...prev, [itemId]: type }));
    toast.success(type === 'up' ? 'Obrigado pelo feedback positivo!' : 'Obrigado pelo feedback!');
  };

  const getRelatedQuestions = (item: FAQItem) => {
    if (!showRelated || !item.relatedQuestions) return [];
    return items.filter(i => item.relatedQuestions?.includes(i.id));
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header e Controles */}
      {(showSearch || showCategories) && (
        <div className="space-y-4">
          {showSearch && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Pesquisar perguntas frequentes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4">
            {showCategories && (
              <div className="flex gap-2 flex-wrap">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category === 'all' ? 'Todas' : category}
                  </Button>
                ))}
              </div>
            )}

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Ordenar por" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="helpful">Mais úteis</SelectItem>
                <SelectItem value="recent">Mais recentes</SelectItem>
                <SelectItem value="alphabetical">Alfabética</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* FAQ Items */}
      <Accordion type="single" collapsible className="w-full space-y-4">
        {filteredAndSortedItems.map(item => {
          const relatedQuestions = getRelatedQuestions(item);
          const userVote = votedItems[item.id];
          
          return (
            <Card key={item.id} className="overflow-hidden">
              <AccordionItem value={item.id} className="border-none">
                <AccordionTrigger className="hover:no-underline px-6 py-4">
                  <div className="flex justify-between items-start w-full mr-4">
                    <div className="text-left">
                      <h3 className="font-medium text-base mb-2">{item.question}</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">{item.category}</Badge>
                        <span className="text-xs text-muted-foreground">
                          👍 {item.helpful} • 👎 {item.notHelpful}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Atualizado em {new Date(item.lastUpdated).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                
                <AccordionContent className="px-6 pb-6">
                  <div className="space-y-4">
                    <p className="text-muted-foreground leading-relaxed">{item.answer}</p>
                    
                    {/* Tags */}
                    {item.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {item.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Voting */}
                    {showVoting && (
                      <div className="flex items-center gap-4 pt-2 border-t">
                        <span className="text-sm text-muted-foreground">Esta resposta foi útil?</span>
                        <div className="flex gap-2">
                          <Button
                            variant={userVote === 'up' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handleVote(item.id, 'up')}
                            disabled={!!userVote}
                          >
                            <ThumbsUp className="h-3 w-3 mr-1" />
                            Sim ({item.helpful})
                          </Button>
                          <Button
                            variant={userVote === 'down' ? 'destructive' : 'outline'}
                            size="sm"
                            onClick={() => handleVote(item.id, 'down')}
                            disabled={!!userVote}
                          >
                            <ThumbsDown className="h-3 w-3 mr-1" />
                            Não ({item.notHelpful})
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Related Questions */}
                    {relatedQuestions.length > 0 && (
                      <div className="pt-4 border-t">
                        <h4 className="text-sm font-medium mb-3">Perguntas relacionadas:</h4>
                        <div className="space-y-2">
                          {relatedQuestions.map(related => (
                            <div key={related.id} className="text-sm">
                              <button className="text-primary hover:underline text-left">
                                {related.question}
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Card>
          );
        })}
      </Accordion>

      {/* No Results */}
      {filteredAndSortedItems.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <MessageCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Nenhuma pergunta encontrada</h3>
            <p className="text-muted-foreground mb-4">
              Não encontramos perguntas que correspondam aos seus critérios de busca.
            </p>
            <Button variant="outline" onClick={() => {
              setSearchQuery('');
              setSelectedCategory('all');
            }}>
              Limpar filtros
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Contact Support */}
      <Card>
        <CardContent className="text-center py-8">
          <h3 className="text-lg font-medium mb-2">Não encontrou sua resposta?</h3>
          <p className="text-muted-foreground mb-4">
            Nossa equipe de suporte está pronta para ajudar com qualquer dúvida específica.
          </p>
          <Button>
            <MessageCircle className="h-4 w-4 mr-2" />
            Contatar Suporte
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
