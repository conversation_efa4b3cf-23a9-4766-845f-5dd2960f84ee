'use client';

import { Home, LayoutDashboard, CreditCard, HelpCircle, Book } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState, useCallback, useRef } from 'react';

// import { DesktopBridgeStatusIndicator } from '@/components/desktop-bridge-status-indicator'; // Removido - não existe mais
import { useMediaQuery } from '@/hooks/use-media-query';
import { ANIMATIONS } from '@/lib/design-tokens';
import { cn } from '@/lib/utils';

const mainNavItems = [
  {
    href: '/',
    label: 'Início',
    icon: Home,
    ariaLabel: 'Página inicial',
  },
  {
    href: '/dashboard',
    label: 'Painel',
    icon: LayoutDashboard,
    ariaLabel: 'Painel de controle',
  },
  {
    href: '/pricing',
    label: 'Preços',
    icon: CreditCard,
    ariaLabel: 'Planos e preços',
  },
  {
    href: '/help',
    label: 'Ajuda',
    icon: HelpCircle,
    ariaLabel: 'Central de ajuda',
  },
  {
    href: '/api-docs',
    label: 'API',
    icon: Book,
    ariaLabel: 'Documentação da API',
  },
];

// Componente de link com estilos consistentes
const LinkComponent = ({
  href,
  children,
  className,
}: {
  href: string;
  children: React.ReactNode;
  className?: string;
}) => {
  // Usar apenas classes base para garantir compatibilidade entre SSR e cliente
  const baseClasses =
    'flex items-center text-lg font-semibold text-foreground hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary';

  return (
    <Link href={href} className={cn(baseClasses, className)}>
      {children}
    </Link>
  );
};

export function NavBar() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [_hasScrolled, setHasScrolled] = useState(false);
  const _prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Refs para gerenciar focus trap no menu mobile
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const firstFocusableElementRef = useRef<HTMLAnchorElement>(null);
  const lastFocusableElementRef = useRef<HTMLButtonElement>(null);

  // Função otimizada para verificar o scroll com debounce
  const checkScroll = useCallback(() => {
    const scrollPosition = window.scrollY;
    setHasScrolled(scrollPosition > 5);
  }, []);

  useEffect(() => {
    checkScroll();

    let timeoutId: NodeJS.Timeout;
    const handleScroll = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(checkScroll, 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [checkScroll]);

  // Fechar o menu mobile quando o caminho muda
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  // Gerenciar foco quando o menu mobile é aberto/fechado
  useEffect(() => {
    if (mobileMenuOpen) {
      // Focar no primeiro elemento quando o menu é aberto
      setTimeout(() => {
        firstFocusableElementRef.current?.focus();
      }, 100);

      // Adicionar listener para capturar Tab e manter foco dentro do menu
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setMobileMenuOpen(false);
          menuButtonRef.current?.focus();
          return;
        }

        if (e.key !== 'Tab') return;

        // Lógica para focus trap
        if (e.shiftKey) {
          // Tab + Shift (para trás)
          if (document.activeElement === firstFocusableElementRef.current) {
            e.preventDefault();
            lastFocusableElementRef.current?.focus();
          }
        } else {
          // Tab (para frente)
          if (document.activeElement === lastFocusableElementRef.current) {
            e.preventDefault();
            firstFocusableElementRef.current?.focus();
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    } else {
      // Quando o menu é fechado, focar no botão do menu
      if (
        document.activeElement === firstFocusableElementRef.current ||
        document.activeElement === lastFocusableElementRef.current
      ) {
        menuButtonRef.current?.focus();
      }
    }
  }, [mobileMenuOpen]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Usar constante de animação do arquivo de design-tokens para consistência
  const motionClass = ANIMATIONS.transition.medium;

  return (
    <>
      {/* Skip link para acessibilidade (pular para o conteúdo principal) */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:outline-ring"
      >
        Skip to main content
      </a>

      <div className="flex items-center w-full">
        <div className="mr-4 flex items-center md:hidden">
          <button
            ref={menuButtonRef}
            type="button"
            onClick={() => toggleMobileMenu()}
            className="inline-flex items-center justify-center rounded-md p-2 text-foreground hover:bg-muted focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
            aria-controls="mobile-menu"
            aria-expanded={mobileMenuOpen}
          >
            <span className="sr-only">{mobileMenuOpen ? 'Close main menu' : 'Open main menu'}</span>
            <svg
              className={cn('h-6 w-6', mobileMenuOpen ? 'hidden' : 'block')}
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
              />
            </svg>
            <svg
              className={cn('h-6 w-6', mobileMenuOpen ? 'block' : 'hidden')}
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex items-center">
          <LinkComponent
            href="/"
            className={cn(
              'flex items-center text-lg font-semibold text-foreground',
              'hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
              motionClass
            )}
          >
            Excel Copilot
          </LinkComponent>
          <div className="hidden md:flex md:items-center md:gap-6 ml-10">
            <LinkComponent
              href="/"
              className={cn(
                'text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
                pathname === '/' ? 'text-primary font-semibold' : 'text-foreground/80',
                motionClass
              )}
              aria-current={pathname === '/' ? 'page' : undefined}
            >
              Início
            </LinkComponent>
            <LinkComponent
              href="/dashboard"
              className={cn(
                'text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
                pathname === '/dashboard' ? 'text-primary font-semibold' : 'text-foreground/80',
                motionClass
              )}
              aria-current={pathname === '/dashboard' ? 'page' : undefined}
            >
              Painel
            </LinkComponent>
            <LinkComponent
              href="/pricing"
              className={cn(
                'text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
                pathname === '/pricing' ? 'text-primary font-semibold' : 'text-foreground/80',
                motionClass
              )}
              aria-current={pathname === '/pricing' ? 'page' : undefined}
            >
              Preços
            </LinkComponent>
            <LinkComponent
              href="/help"
              className={cn(
                'text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
                pathname === '/help' ? 'text-primary font-semibold' : 'text-foreground/80',
                motionClass
              )}
              aria-current={pathname === '/help' ? 'page' : undefined}
            >
              Ajuda
            </LinkComponent>
            <LinkComponent
              href="/api-docs"
              className={cn(
                'text-sm font-medium hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
                pathname === '/api-docs' ? 'text-primary font-semibold' : 'text-foreground/80',
                motionClass
              )}
              aria-current={pathname === '/api-docs' ? 'page' : undefined}
            >
              API
            </LinkComponent>
          </div>
        </div>

        {/* Adicionar espaço flexível entre os elementos */}
        <div className="flex-grow min-w-[120px] md:min-w-[200px] lg:min-w-[300px]"></div>

        {/* Área de ferramentas e status à direita */}
        <div className="flex items-center gap-3 md:gap-5">
          {/* Indicador de status do Excel Desktop removido - não é mais necessário */}
        </div>
      </div>

      {/* Menu Mobile */}
      {mobileMenuOpen && (
        <div
          ref={mobileMenuRef}
          id="mobile-menu"
          className="md:hidden fixed inset-0 top-16 z-40 bg-background/80 backdrop-blur-sm"
          aria-label="Mobile Navigation"
        >
          <div className="p-4 bg-background border-b shadow-md flex flex-col space-y-4">
            {mainNavItems.map((item, index) => {
              const isFirst = index === 0;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'flex items-center py-2 text-base font-medium rounded-md',
                    pathname === item.href
                      ? 'text-primary font-semibold'
                      : 'text-foreground/70 hover:text-primary',
                    motionClass
                  )}
                  aria-current={pathname === item.href ? 'page' : undefined}
                  ref={isFirst ? firstFocusableElementRef : undefined}
                >
                  <item.icon className="mr-3 h-5 w-5" aria-hidden="true" />
                  {item.label}
                </Link>
              );
            })}

            <div className="pt-4 border-t border-border">
              <button
                ref={lastFocusableElementRef}
                onClick={() => toggleMobileMenu()}
                className="w-full flex items-center justify-center px-4 py-2 text-base font-medium text-foreground bg-muted rounded-md hover:bg-muted/80"
              >
                Fechar Menu
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
