'use client';

import { useState } from 'react';
import { Search, Book, MessageCircle, FileText, Video, ExternalLink, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface HelpArticle {
  id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  content: string;
  lastUpdated: string;
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
}

const HELP_ARTICLES: HelpArticle[] = [
  {
    id: '1',
    title: 'Primeiros Passos com Excel Copilot',
    description: 'Aprenda como começar a usar o Excel Copilot para automatizar suas planilhas',
    category: 'Iniciante',
    tags: ['tutorial', 'básico', 'primeiros-passos'],
    content: 'Guia completo para iniciantes...',
    lastUpdated: '2025-06-18'
  },
  {
    id: '2',
    title: 'Comandos de IA Avançados',
    description: 'Domine os comandos avançados para manipulação de dados com IA',
    category: 'Avançado',
    tags: ['ia', 'comandos', 'avançado'],
    content: 'Lista completa de comandos avançados...',
    lastUpdated: '2025-06-18'
  },
  {
    id: '3',
    title: 'Integração com APIs Externas',
    description: 'Como conectar suas planilhas com APIs externas',
    category: 'Integração',
    tags: ['api', 'integração', 'webhook'],
    content: 'Tutorial de integração com APIs...',
    lastUpdated: '2025-06-18'
  },
  {
    id: '4',
    title: 'Solução de Problemas Comuns',
    description: 'Resolva os problemas mais frequentes do Excel Copilot',
    category: 'Solução de Problemas',
    tags: ['troubleshooting', 'problemas', 'suporte'],
    content: 'Guia de solução de problemas...',
    lastUpdated: '2025-06-18'
  }
];

const FAQ_ITEMS: FAQItem[] = [
  {
    id: '1',
    question: 'Como posso começar a usar o Excel Copilot?',
    answer: 'Para começar, faça login na plataforma e crie sua primeira planilha. Use o painel de chat à direita para dar comandos em linguagem natural como "Adicione uma coluna Total" ou "Calcule a média dos valores".',
    category: 'Iniciante',
    helpful: 45
  },
  {
    id: '2',
    question: 'Quais formatos de arquivo são suportados?',
    answer: 'O Excel Copilot suporta arquivos .xlsx, .xls e .csv. Você pode importar e exportar em todos esses formatos.',
    category: 'Arquivos',
    helpful: 32
  },
  {
    id: '3',
    question: 'Como funciona a colaboração em tempo real?',
    answer: 'A colaboração permite que múltiplos usuários trabalhem na mesma planilha simultaneamente. As mudanças são sincronizadas automaticamente e você pode ver quem está editando em tempo real.',
    category: 'Colaboração',
    helpful: 28
  },
  {
    id: '4',
    question: 'Existe limite para o tamanho das planilhas?',
    answer: 'O limite depende do seu plano. O plano gratuito suporta até 1000 linhas, o plano Pro até 10.000 linhas e o plano Enterprise não tem limite.',
    category: 'Limites',
    helpful: 19
  },
  {
    id: '5',
    question: 'Como posso exportar minha planilha?',
    answer: 'Use o botão "Exportar" na barra de ferramentas e escolha entre os formatos Excel (.xlsx) ou CSV (.csv). O download começará automaticamente.',
    category: 'Exportação',
    helpful: 41
  }
];

const QUICK_LINKS = [
  { title: 'Documentação da API', href: '/api-docs', icon: FileText },
  { title: 'Exemplos e Templates', href: '/examples', icon: Book },
  { title: 'Vídeos Tutoriais', href: '#', icon: Video },
  { title: 'Contato Suporte', href: '#', icon: MessageCircle }
];

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredArticles = HELP_ARTICLES.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const filteredFAQ = FAQ_ITEMS.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const categories = ['all', ...Array.from(new Set([...HELP_ARTICLES.map(a => a.category), ...FAQ_ITEMS.map(f => f.category)]))];

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Central de Ajuda</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Encontre respostas, tutoriais e documentação para o Excel Copilot
        </p>
        
        {/* Search Bar */}
        <div className="relative max-w-2xl mx-auto mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Pesquisar na documentação..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-12 text-lg"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              {category === 'all' ? 'Todas' : category}
            </Button>
          ))}
        </div>
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {QUICK_LINKS.map((link, index) => {
          const Icon = link.icon;
          return (
            <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Icon className="h-8 w-8 mx-auto mb-3 text-primary" />
                <h3 className="font-semibold mb-2">{link.title}</h3>
                <Button variant="ghost" size="sm" className="text-primary">
                  Acessar <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      <Tabs defaultValue="articles" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="articles">Artigos e Tutoriais</TabsTrigger>
          <TabsTrigger value="faq">Perguntas Frequentes</TabsTrigger>
        </TabsList>

        <TabsContent value="articles" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredArticles.map(article => (
              <Card key={article.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary">{article.category}</Badge>
                    <span className="text-sm text-muted-foreground">{article.lastUpdated}</span>
                  </div>
                  <CardTitle className="text-lg">{article.title}</CardTitle>
                  <CardDescription>{article.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-1 mb-4">
                    {article.tags.map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    Ler Artigo <ChevronRight className="h-3 w-3 ml-1" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="faq" className="space-y-6">
          <Accordion type="single" collapsible className="w-full">
            {filteredFAQ.map(faq => (
              <AccordionItem key={faq.id} value={faq.id}>
                <AccordionTrigger className="text-left">
                  <div className="flex justify-between items-center w-full mr-4">
                    <span>{faq.question}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">{faq.category}</Badge>
                      <span className="text-xs text-muted-foreground">👍 {faq.helpful}</span>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="pt-2">
                    <p className="text-muted-foreground mb-4">{faq.answer}</p>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        👍 Útil
                      </Button>
                      <Button variant="outline" size="sm">
                        👎 Não útil
                      </Button>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </TabsContent>
      </Tabs>

      {/* Contact Support */}
      <div className="mt-12 text-center">
        <Card className="max-w-2xl mx-auto">
          <CardContent className="p-8">
            <h3 className="text-xl font-semibold mb-4">Não encontrou o que procurava?</h3>
            <p className="text-muted-foreground mb-6">
              Nossa equipe de suporte está pronta para ajudar você com qualquer dúvida.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button>
                <MessageCircle className="h-4 w-4 mr-2" />
                Contatar Suporte
              </Button>
              <Button variant="outline">
                <Book className="h-4 w-4 mr-2" />
                Documentação Completa
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
