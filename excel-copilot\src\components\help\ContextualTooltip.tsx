'use client';

import { useState, useEffect } from 'react';
import { HelpCircle, Info, Lightbulb, Keyboard, ExternalLink, X } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface TooltipData {
  id: string;
  title: string;
  description: string;
  type: 'info' | 'tip' | 'warning' | 'shortcut' | 'feature';
  content: {
    text?: string;
    steps?: string[];
    shortcuts?: Array<{ keys: string[]; description: string; mac?: string[] }>;
    examples?: Array<{ title: string; description: string; code?: string }>;
    links?: Array<{ title: string; url: string; external?: boolean }>;
  };
  category: string;
  showCount?: number;
  lastShown?: string;
}

interface ContextualTooltipProps {
  helpId: string;
  children: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  className?: string;
  showIcon?: boolean;
  iconType?: 'help' | 'info' | 'tip';
  delayDuration?: number;
  rich?: boolean;
  onShow?: () => void;
  onHide?: () => void;
}

// Base de dados de tooltips contextuais
const TOOLTIP_DATABASE: Record<string, TooltipData> = {
  'chat-input': {
    id: 'chat-input',
    title: 'Chat com IA',
    description: 'Use linguagem natural para manipular sua planilha',
    type: 'feature',
    content: {
      text: 'Digite comandos em português para que a IA execute automaticamente na sua planilha.',
      examples: [
        { title: 'Cálculos', description: 'Calcule a soma da coluna A' },
        { title: 'Formatação', description: 'Destaque em verde valores acima de 1000' },
        { title: 'Gráficos', description: 'Crie um gráfico de barras com os dados' },
        { title: 'Análise', description: 'Encontre a média e mediana dos valores' }
      ],
      shortcuts: [
        { keys: ['Enter'], description: 'Enviar comando', mac: ['Enter'] },
        { keys: ['Shift', 'Enter'], description: 'Nova linha', mac: ['Shift', 'Enter'] },
        { keys: ['Ctrl', 'K'], description: 'Abrir paleta de comandos', mac: ['⌘', 'K'] }
      ],
      links: [
        { title: 'Ver todos os comandos', url: '/help#commands' },
        { title: 'Exemplos práticos', url: '/examples' }
      ]
    },
    category: 'IA'
  },
  'export-button': {
    id: 'export-button',
    title: 'Exportar Planilha',
    description: 'Baixe sua planilha em diferentes formatos',
    type: 'feature',
    content: {
      text: 'Exporte sua planilha nos formatos Excel (.xlsx) ou CSV (.csv) para usar em outros programas.',
      steps: [
        'Clique no botão Exportar',
        'Escolha o formato desejado (Excel ou CSV)',
        'O download começará automaticamente',
        'Abra o arquivo no programa de sua preferência'
      ],
      examples: [
        { title: 'Excel (.xlsx)', description: 'Mantém formatação, fórmulas e gráficos' },
        { title: 'CSV (.csv)', description: 'Formato universal, apenas dados' }
      ],
      links: [
        { title: 'Formatos suportados', url: '/help#file-formats' }
      ]
    },
    category: 'Arquivos'
  },
  'collaboration-panel': {
    id: 'collaboration-panel',
    title: 'Colaboração em Tempo Real',
    description: 'Trabalhe em equipe na mesma planilha',
    type: 'feature',
    content: {
      text: 'Veja quem está editando em tempo real e colabore de forma eficiente.',
      steps: [
        'Clique em "Compartilhar" para convidar pessoas',
        'Defina as permissões (visualizar, editar, comentar)',
        'Envie o convite por email ou copie o link',
        'Colaboradores aparecerão com cursores coloridos'
      ],
      shortcuts: [
        { keys: ['Ctrl', 'Shift', 'S'], description: 'Abrir painel de compartilhamento', mac: ['⌘', '⇧', 'S'] }
      ],
      links: [
        { title: 'Guia de colaboração', url: '/help/collaboration' }
      ]
    },
    category: 'Colaboração'
  },
  'keyboard-shortcuts': {
    id: 'keyboard-shortcuts',
    title: 'Atalhos de Teclado',
    description: 'Trabalhe mais rápido com atalhos',
    type: 'shortcut',
    content: {
      text: 'Use atalhos para acelerar seu trabalho com planilhas.',
      shortcuts: [
        { keys: ['Ctrl', 'S'], description: 'Salvar planilha', mac: ['⌘', 'S'] },
        { keys: ['Ctrl', 'Z'], description: 'Desfazer', mac: ['⌘', 'Z'] },
        { keys: ['Ctrl', 'Y'], description: 'Refazer', mac: ['⌘', 'Y'] },
        { keys: ['F11'], description: 'Tela cheia', mac: ['F11'] },
        { keys: ['?'], description: 'Mostrar todos os atalhos', mac: ['?'] }
      ],
      links: [
        { title: 'Lista completa de atalhos', url: '/help#shortcuts' }
      ]
    },
    category: 'Produtividade'
  },
  'ai-status': {
    id: 'ai-status',
    title: 'Status da IA',
    description: 'Indicador do estado dos serviços de IA',
    type: 'info',
    content: {
      text: 'Este indicador mostra se os serviços de IA estão funcionando corretamente.',
      examples: [
        { title: '🟢 Online', description: 'IA funcionando normalmente' },
        { title: '🟡 Lento', description: 'IA com resposta mais lenta' },
        { title: '🔴 Offline', description: 'IA temporariamente indisponível' }
      ],
      links: [
        { title: 'Status dos serviços', url: '/status', external: true }
      ]
    },
    category: 'Sistema'
  },
  'formula-bar': {
    id: 'formula-bar',
    title: 'Barra de Fórmulas',
    description: 'Edite fórmulas e valores das células',
    type: 'feature',
    content: {
      text: 'Use a barra de fórmulas para inserir e editar fórmulas complexas.',
      examples: [
        { title: 'Soma', description: '=SOMA(A1:A10)', code: '=SOMA(A1:A10)' },
        { title: 'Média', description: '=MÉDIA(B1:B10)', code: '=MÉDIA(B1:B10)' },
        { title: 'Condicional', description: '=SE(C1>100,"Alto","Baixo")', code: '=SE(C1>100,"Alto","Baixo")' }
      ],
      shortcuts: [
        { keys: ['F2'], description: 'Editar célula', mac: ['F2'] },
        { keys: ['Esc'], description: 'Cancelar edição', mac: ['Esc'] },
        { keys: ['Enter'], description: 'Confirmar fórmula', mac: ['Enter'] }
      ],
      links: [
        { title: 'Guia de fórmulas', url: '/help/formulas' }
      ]
    },
    category: 'Fórmulas'
  }
};

const ICON_TYPES = {
  help: HelpCircle,
  info: Info,
  tip: Lightbulb
};

const TYPE_COLORS = {
  info: 'text-blue-500',
  tip: 'text-yellow-500',
  warning: 'text-red-500',
  shortcut: 'text-purple-500',
  feature: 'text-green-500'
};

export function ContextualTooltip({
  helpId,
  children,
  side = 'top',
  align = 'center',
  className = '',
  showIcon = true,
  iconType = 'help',
  delayDuration = 300,
  rich = true,
  onShow,
  onHide
}: ContextualTooltipProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tooltipData, setTooltipData] = useState<TooltipData | null>(null);

  useEffect(() => {
    const data = TOOLTIP_DATABASE[helpId];
    if (data) {
      setTooltipData(data);
    }
  }, [helpId]);

  useEffect(() => {
    if (isOpen && onShow) {
      onShow();
    } else if (!isOpen && onHide) {
      onHide();
    }
  }, [isOpen, onShow, onHide]);

  if (!tooltipData) {
    return <>{children}</>;
  }

  const Icon = ICON_TYPES[iconType];

  const renderSimpleTooltip = () => (
    <TooltipContent side={side} align={align} className={className}>
      <div className="max-w-xs">
        <p className="font-medium">{tooltipData.title}</p>
        <p className="text-sm text-muted-foreground mt-1">{tooltipData.description}</p>
      </div>
    </TooltipContent>
  );

  const renderRichTooltip = () => (
    <TooltipContent side={side} align={align} className={`p-0 max-w-md ${className}`} asChild>
      <Card className="border shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-base flex items-center gap-2">
                <span className={TYPE_COLORS[tooltipData.type]}>{tooltipData.title}</span>
                <Badge variant="outline" className="text-xs">
                  {tooltipData.category}
                </Badge>
              </CardTitle>
              <CardDescription className="mt-1">
                {tooltipData.description}
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Texto principal */}
          {tooltipData.content.text && (
            <p className="text-sm text-muted-foreground">
              {tooltipData.content.text}
            </p>
          )}

          {/* Passos */}
          {tooltipData.content.steps && (
            <div>
              <h4 className="text-sm font-medium mb-2">Como usar:</h4>
              <ol className="text-sm space-y-1">
                {tooltipData.content.steps.map((step, index) => (
                  <li key={index} className="flex gap-2">
                    <span className="text-primary font-medium">{index + 1}.</span>
                    <span className="text-muted-foreground">{step}</span>
                  </li>
                ))}
              </ol>
            </div>
          )}

          {/* Exemplos */}
          {tooltipData.content.examples && (
            <div>
              <h4 className="text-sm font-medium mb-2">Exemplos:</h4>
              <div className="space-y-2">
                {tooltipData.content.examples.map((example, index) => (
                  <div key={index} className="text-sm">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{example.title}:</span>
                      <span className="text-muted-foreground">{example.description}</span>
                    </div>
                    {example.code && (
                      <code className="text-xs bg-muted px-2 py-1 rounded mt-1 block">
                        {example.code}
                      </code>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Atalhos */}
          {tooltipData.content.shortcuts && (
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
                <Keyboard className="h-3 w-3" />
                Atalhos:
              </h4>
              <div className="space-y-2">
                {tooltipData.content.shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">{shortcut.description}</span>
                    <div className="flex gap-1">
                      {(shortcut.mac && navigator.platform.toUpperCase().indexOf('MAC') >= 0 
                        ? shortcut.mac 
                        : shortcut.keys
                      ).map((key, keyIndex) => (
                        <Badge key={keyIndex} variant="outline" className="text-xs px-1 py-0">
                          {key}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Links */}
          {tooltipData.content.links && (
            <>
              <Separator />
              <div className="space-y-2">
                {tooltipData.content.links.map((link, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start h-auto p-2 text-sm"
                    onClick={() => {
                      if (link.external) {
                        window.open(link.url, '_blank');
                      } else {
                        window.location.href = link.url;
                      }
                    }}
                  >
                    <span className="flex-1 text-left">{link.title}</span>
                    <ExternalLink className="h-3 w-3 ml-2" />
                  </Button>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </TooltipContent>
  );

  return (
    <TooltipProvider delayDuration={delayDuration}>
      <Tooltip open={isOpen} onOpenChange={setIsOpen}>
        <TooltipTrigger asChild>
          <div className="inline-flex items-center gap-1">
            {children}
            {showIcon && (
              <Icon className={`h-4 w-4 ${TYPE_COLORS[tooltipData.type]} cursor-help`} />
            )}
          </div>
        </TooltipTrigger>
        {rich ? renderRichTooltip() : renderSimpleTooltip()}
      </Tooltip>
    </TooltipProvider>
  );
}

// Hook para rastrear uso de tooltips
export function useTooltipAnalytics() {
  const trackTooltipShow = (helpId: string) => {
    // Analytics tracking
    if (typeof window !== 'undefined' && 'gtag' in window) {
      const windowWithGtag = window as any;
      const gtag = windowWithGtag.gtag;
      if (typeof gtag === 'function') {
        gtag('event', 'tooltip_show', {
          help_id: helpId,
          timestamp: new Date().toISOString()
        });
      }
    }
  };

  const trackTooltipHide = (helpId: string, duration: number) => {
    // Analytics tracking
    if (typeof window !== 'undefined' && 'gtag' in window) {
      const windowWithGtag = window as any;
      const gtag = windowWithGtag.gtag;
      if (typeof gtag === 'function') {
        gtag('event', 'tooltip_hide', {
          help_id: helpId,
          duration_ms: duration,
          timestamp: new Date().toISOString()
        });
      }
    }
  };

  return { trackTooltipShow, trackTooltipHide };
}

// Componente para tooltip rápido
export function QuickTooltip({ 
  content, 
  children, 
  side = 'top' 
}: { 
  content: string; 
  children: React.ReactNode; 
  side?: 'top' | 'right' | 'bottom' | 'left';
}) {
  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent side={side}>
          <p className="text-sm">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
