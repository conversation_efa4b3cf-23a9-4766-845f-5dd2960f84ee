'use client';

import { useState, useEffect, useMemo } from 'react';
import { Search, Filter, Clock, BookOpen, MessageCircle, Code, FileText, Zap } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  content: string;
  type: 'article' | 'api' | 'faq' | 'tutorial' | 'example';
  category: string;
  tags: string[];
  url: string;
  lastUpdated: string;
  relevanceScore: number;
  highlights?: string[];
}

interface SearchFilters {
  types: string[];
  categories: string[];
  dateRange: string;
  sortBy: string;
}

const SEARCH_DATA: SearchResult[] = [
  {
    id: '1',
    title: 'Primeiros Passos com Excel Copilot',
    description: 'Guia completo para começar a usar o Excel Copilot',
    content: 'Aprenda como fazer login, criar planilhas e usar comandos de IA para automatizar suas tarefas...',
    type: 'tutorial',
    category: 'Iniciante',
    tags: ['tutorial', 'básico', 'login', 'comandos'],
    url: '/help#tutorial-1',
    lastUpdated: '2025-06-18',
    relevanceScore: 0.95
  },
  {
    id: '2',
    title: 'API de Workbooks',
    description: 'Documentação completa da API para gerenciar planilhas',
    content: 'Endpoints para criar, listar, atualizar e excluir planilhas. Inclui exemplos de código...',
    type: 'api',
    category: 'API',
    tags: ['api', 'workbooks', 'crud', 'endpoints'],
    url: '/api-docs#workbooks',
    lastUpdated: '2025-06-18',
    relevanceScore: 0.88
  },
  {
    id: '3',
    title: 'Como usar comandos de IA?',
    description: 'Pergunta frequente sobre comandos de inteligência artificial',
    content: 'Você pode usar comandos como: Calcule a soma da coluna A, Crie um gráfico de barras...',
    type: 'faq',
    category: 'IA',
    tags: ['ia', 'comandos', 'linguagem-natural'],
    url: '/help#faq-3',
    lastUpdated: '2025-06-18',
    relevanceScore: 0.92
  },
  {
    id: '4',
    title: 'Exemplo: Análise de Vendas',
    description: 'Template pronto para análise de dados de vendas',
    content: 'Template com fórmulas, gráficos e análises automáticas para dados de vendas...',
    type: 'example',
    category: 'Templates',
    tags: ['template', 'vendas', 'análise', 'gráficos'],
    url: '/examples/sales-analysis',
    lastUpdated: '2025-06-17',
    relevanceScore: 0.85
  },
  {
    id: '5',
    title: 'Integração com APIs Externas',
    description: 'Como conectar planilhas com APIs de terceiros',
    content: 'Tutorial passo-a-passo para integrar dados de APIs externas em suas planilhas...',
    type: 'article',
    category: 'Integração',
    tags: ['api', 'integração', 'webhook', 'dados'],
    url: '/help/api-integration',
    lastUpdated: '2025-06-16',
    relevanceScore: 0.78
  },
  {
    id: '6',
    title: 'Endpoint de Chat com IA',
    description: 'API para enviar comandos para processamento por IA',
    content: 'POST /api/chat - Envia mensagem para IA processar e manipular planilha...',
    type: 'api',
    category: 'API',
    tags: ['api', 'chat', 'ia', 'processamento'],
    url: '/api-docs#chat',
    lastUpdated: '2025-06-18',
    relevanceScore: 0.90
  },
  {
    id: '7',
    title: 'Colaboração em Tempo Real',
    description: 'Como trabalhar em equipe nas planilhas',
    content: 'Funcionalidades de colaboração, compartilhamento e sincronização em tempo real...',
    type: 'tutorial',
    category: 'Colaboração',
    tags: ['colaboração', 'tempo-real', 'compartilhamento'],
    url: '/help/collaboration',
    lastUpdated: '2025-06-17',
    relevanceScore: 0.82
  },
  {
    id: '8',
    title: 'Formatos de arquivo suportados',
    description: 'Quais tipos de arquivo posso importar e exportar?',
    content: 'O Excel Copilot suporta .xlsx, .xls, .csv para importação e exportação...',
    type: 'faq',
    category: 'Arquivos',
    tags: ['formatos', 'xlsx', 'csv', 'import', 'export'],
    url: '/help#faq-2',
    lastUpdated: '2025-06-18',
    relevanceScore: 0.87
  }
];

const TYPE_ICONS = {
  article: BookOpen,
  api: Code,
  faq: MessageCircle,
  tutorial: Zap,
  example: FileText
};

const TYPE_LABELS = {
  article: 'Artigo',
  api: 'API',
  faq: 'FAQ',
  tutorial: 'Tutorial',
  example: 'Exemplo'
};

interface SearchDocsProps {
  onResultClick?: (result: SearchResult) => void;
  maxResults?: number;
  showFilters?: boolean;
  className?: string;
}

export function SearchDocs({ 
  onResultClick, 
  maxResults = 50, 
  showFilters = true,
  className = '' 
}: SearchDocsProps) {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({
    types: [],
    categories: [],
    dateRange: 'all',
    sortBy: 'relevance'
  });
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Carregar pesquisas recentes do localStorage
  useEffect(() => {
    const saved = localStorage.getItem('excel-copilot-recent-searches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Salvar pesquisa recente
  const saveRecentSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) return;
    
    const updated = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('excel-copilot-recent-searches', JSON.stringify(updated));
  };

  // Filtrar e ordenar resultados
  const filteredResults = useMemo(() => {
    let results = SEARCH_DATA;

    // Filtro por query
    if (query.trim()) {
      const searchTerms = query.toLowerCase().split(' ');
      results = results.filter(item => {
        const searchableText = `${item.title} ${item.description} ${item.content} ${item.tags.join(' ')}`.toLowerCase();
        return searchTerms.every(term => searchableText.includes(term));
      }).map(item => {
        // Calcular relevância baseada na query
        const titleMatch = item.title.toLowerCase().includes(query.toLowerCase()) ? 0.3 : 0;
        const descMatch = item.description.toLowerCase().includes(query.toLowerCase()) ? 0.2 : 0;
        const contentMatch = item.content.toLowerCase().includes(query.toLowerCase()) ? 0.1 : 0;
        const tagMatch = item.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())) ? 0.2 : 0;
        
        return {
          ...item,
          relevanceScore: item.relevanceScore + titleMatch + descMatch + contentMatch + tagMatch,
          highlights: extractHighlights(item, query)
        };
      });
    }

    // Filtros por tipo
    if (filters.types.length > 0) {
      results = results.filter(item => filters.types.includes(item.type));
    }

    // Filtros por categoria
    if (filters.categories.length > 0) {
      results = results.filter(item => filters.categories.includes(item.category));
    }

    // Filtro por data
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const cutoffDate = new Date();
      
      switch (filters.dateRange) {
        case 'week':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          cutoffDate.setMonth(now.getMonth() - 3);
          break;
      }
      
      results = results.filter(item => new Date(item.lastUpdated) >= cutoffDate);
    }

    // Ordenação
    results.sort((a, b) => {
      switch (filters.sortBy) {
        case 'relevance':
          return b.relevanceScore - a.relevanceScore;
        case 'date':
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    return results.slice(0, maxResults);
  }, [query, filters, maxResults]);

  // Extrair highlights da busca
  const extractHighlights = (item: SearchResult, searchQuery: string): string[] => {
    const highlights: string[] = [];
    const terms = searchQuery.toLowerCase().split(' ');
    
    terms.forEach(term => {
      if (item.title.toLowerCase().includes(term)) {
        highlights.push(`Título: "${item.title}"`);
      }
      if (item.description.toLowerCase().includes(term)) {
        highlights.push(`Descrição: "${item.description.substring(0, 100)}..."`);
      }
    });
    
    return highlights.slice(0, 2);
  };

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
    if (searchQuery.trim()) {
      saveRecentSearch(searchQuery);
    }
  };

  const handleResultClick = (result: SearchResult) => {
    if (onResultClick) {
      onResultClick(result);
    } else {
      window.open(result.url, '_blank');
    }
  };

  const allTypes = Array.from(new Set(SEARCH_DATA.map(item => item.type)));
  const allCategories = Array.from(new Set(SEARCH_DATA.map(item => item.category)));

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Pesquisar na documentação..."
          value={query}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10 h-12 text-lg"
        />
      </div>

      {/* Recent Searches */}
      {!query && recentSearches.length > 0 && (
        <div>
          <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Pesquisas recentes
          </h3>
          <div className="flex flex-wrap gap-2">
            {recentSearches.map((search, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => handleSearch(search)}
                className="text-xs"
              >
                {search}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filtros
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Tipos */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Tipo de Conteúdo</Label>
                <div className="space-y-2">
                  {allTypes.map(type => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={filters.types.includes(type)}
                        onCheckedChange={(checked) => {
                          setFilters(prev => ({
                            ...prev,
                            types: checked 
                              ? [...prev.types, type]
                              : prev.types.filter(t => t !== type)
                          }));
                        }}
                      />
                      <Label htmlFor={`type-${type}`} className="text-sm">
                        {TYPE_LABELS[type as keyof typeof TYPE_LABELS]}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Categorias */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Categoria</Label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {allCategories.map(category => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={`cat-${category}`}
                        checked={filters.categories.includes(category)}
                        onCheckedChange={(checked) => {
                          setFilters(prev => ({
                            ...prev,
                            categories: checked 
                              ? [...prev.categories, category]
                              : prev.categories.filter(c => c !== category)
                          }));
                        }}
                      />
                      <Label htmlFor={`cat-${category}`} className="text-sm">
                        {category}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Data */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Período</Label>
                <Select value={filters.dateRange} onValueChange={(value) => 
                  setFilters(prev => ({ ...prev, dateRange: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos</SelectItem>
                    <SelectItem value="week">Última semana</SelectItem>
                    <SelectItem value="month">Último mês</SelectItem>
                    <SelectItem value="quarter">Últimos 3 meses</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Ordenação */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Ordenar por</Label>
                <Select value={filters.sortBy} onValueChange={(value) => 
                  setFilters(prev => ({ ...prev, sortBy: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">Relevância</SelectItem>
                    <SelectItem value="date">Data</SelectItem>
                    <SelectItem value="title">Título</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Clear Filters */}
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setFilters({ types: [], categories: [], dateRange: 'all', sortBy: 'relevance' })}
            >
              Limpar Filtros
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <div className="space-y-4">
        {query && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {filteredResults.length} resultado{filteredResults.length !== 1 ? 's' : ''} encontrado{filteredResults.length !== 1 ? 's' : ''} para "{query}"
            </p>
          </div>
        )}

        {filteredResults.map(result => {
          const Icon = TYPE_ICONS[result.type];
          return (
            <Card key={result.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleResultClick(result)}>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    <Icon className="h-5 w-5 text-primary mt-1" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium text-lg truncate">{result.title}</h3>
                      <Badge variant="outline" className="text-xs">
                        {TYPE_LABELS[result.type as keyof typeof TYPE_LABELS]}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {result.category}
                      </Badge>
                    </div>
                    
                    <p className="text-muted-foreground mb-3 line-clamp-2">
                      {result.description}
                    </p>

                    {result.highlights && result.highlights.length > 0 && (
                      <div className="mb-3">
                        {result.highlights.map((highlight, index) => (
                          <p key={index} className="text-xs text-primary bg-primary/10 px-2 py-1 rounded mb-1">
                            {highlight}
                          </p>
                        ))}
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {result.tags.slice(0, 3).map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {result.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{result.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(result.lastUpdated).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}

        {query && filteredResults.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Nenhum resultado encontrado</h3>
              <p className="text-muted-foreground mb-4">
                Tente usar termos diferentes ou remover alguns filtros.
              </p>
              <Button variant="outline" onClick={() => setQuery('')}>
                Limpar busca
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
