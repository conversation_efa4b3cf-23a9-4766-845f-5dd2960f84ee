'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Search, 
  Code, 
  Play, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Lock, 
  Globe,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { toast } from 'sonner';

interface APIEndpoint {
  path: string;
  method: string;
  summary: string;
  description: string;
  tags: string[];
  requiresAuth: boolean;
  adminOnly?: boolean;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  requestBody?: {
    type: string;
    example: string;
  };
  responses?: Array<{
    status: number;
    description: string;
    example: string;
  }>;
}

const API_ENDPOINTS: APIEndpoint[] = [
  {
    path: '/api/auth/login',
    method: 'POST',
    summary: 'Login de usuário',
    description: 'Autentica um usuário e retorna token de acesso',
    tags: ['Auth'],
    requiresAuth: false,
    requestBody: {
      type: 'application/json',
      example: JSON.stringify({ email: '<EMAIL>', password: 'password123' }, null, 2)
    },
    responses: [
      {
        status: 200,
        description: 'Login realizado com sucesso',
        example: JSON.stringify({ success: true, token: 'jwt_token_here', user: { id: '1', email: '<EMAIL>' } }, null, 2)
      },
      {
        status: 401,
        description: 'Credenciais inválidas',
        example: JSON.stringify({ success: false, error: 'Credenciais inválidas' }, null, 2)
      }
    ]
  },
  {
    path: '/api/workbooks',
    method: 'GET',
    summary: 'Listar planilhas',
    description: 'Lista todas as planilhas do usuário autenticado',
    tags: ['Workbooks'],
    requiresAuth: true,
    parameters: [
      { name: 'page', type: 'number', required: false, description: 'Número da página (padrão: 1)' },
      { name: 'limit', type: 'number', required: false, description: 'Itens por página (padrão: 10)' }
    ],
    responses: [
      {
        status: 200,
        description: 'Lista de planilhas retornada com sucesso',
        example: JSON.stringify({ 
          success: true, 
          data: [
            { id: '1', name: 'Vendas Q1', createdAt: '2025-06-18T10:00:00Z' },
            { id: '2', name: 'Relatório Mensal', createdAt: '2025-06-17T15:30:00Z' }
          ],
          pagination: { page: 1, limit: 10, total: 2 }
        }, null, 2)
      }
    ]
  },
  {
    path: '/api/workbooks',
    method: 'POST',
    summary: 'Criar planilha',
    description: 'Cria uma nova planilha para o usuário autenticado',
    tags: ['Workbooks'],
    requiresAuth: true,
    requestBody: {
      type: 'application/json',
      example: JSON.stringify({ 
        name: 'Nova Planilha',
        description: 'Descrição da planilha',
        template: 'blank'
      }, null, 2)
    },
    responses: [
      {
        status: 201,
        description: 'Planilha criada com sucesso',
        example: JSON.stringify({ 
          success: true, 
          data: { 
            id: '3', 
            name: 'Nova Planilha', 
            description: 'Descrição da planilha',
            createdAt: '2025-06-18T12:00:00Z' 
          }
        }, null, 2)
      }
    ]
  },
  {
    path: '/api/chat',
    method: 'POST',
    summary: 'Chat com IA',
    description: 'Envia mensagem para processamento por IA e manipulação de planilha',
    tags: ['AI'],
    requiresAuth: true,
    requestBody: {
      type: 'application/json',
      example: JSON.stringify({ 
        message: 'Adicione uma coluna Total que some as colunas A e B',
        workbookId: '1',
        context: 'spreadsheet'
      }, null, 2)
    },
    responses: [
      {
        status: 200,
        description: 'Comando processado com sucesso',
        example: JSON.stringify({ 
          success: true, 
          data: { 
            response: 'Coluna Total adicionada com sucesso!',
            operations: ['add_column'],
            result: { columnAdded: 'C', formula: '=A1+B1' }
          }
        }, null, 2)
      }
    ]
  },
  {
    path: '/api/excel/analyze',
    method: 'POST',
    summary: 'Analisar dados',
    description: 'Analisa dados da planilha para obter insights e estatísticas',
    tags: ['Excel'],
    requiresAuth: true,
    requestBody: {
      type: 'application/json',
      example: JSON.stringify({ 
        workbookId: '1',
        sheetName: 'Sheet1',
        range: 'A1:C10',
        analysisType: 'statistics'
      }, null, 2)
    },
    responses: [
      {
        status: 200,
        description: 'Análise concluída com sucesso',
        example: JSON.stringify({ 
          success: true, 
          data: { 
            statistics: { mean: 45.6, median: 42, mode: 40, stdDev: 12.3 },
            insights: ['Dados mostram tendência crescente', 'Outliers detectados na linha 7'],
            charts: [{ type: 'line', data: '...' }]
          }
        }, null, 2)
      }
    ]
  },
  {
    path: '/api/metrics',
    method: 'GET',
    summary: 'Métricas do Sistema',
    description: 'Obtém métricas de desempenho da aplicação (apenas administradores)',
    tags: ['Admin'],
    requiresAuth: true,
    adminOnly: true,
    responses: [
      {
        status: 200,
        description: 'Métricas retornadas com sucesso',
        example: JSON.stringify({ 
          success: true, 
          data: { 
            activeUsers: 1250,
            totalWorkbooks: 5430,
            apiCalls: 125000,
            uptime: '99.9%'
          }
        }, null, 2)
      }
    ]
  }
];

const METHOD_COLORS = {
  GET: 'bg-green-100 text-green-800 border-green-200',
  POST: 'bg-blue-100 text-blue-800 border-blue-200',
  PUT: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  PATCH: 'bg-orange-100 text-orange-800 border-orange-200',
  DELETE: 'bg-red-100 text-red-800 border-red-200'
};

export default function APIDocsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTag, setSelectedTag] = useState('all');
  const [expandedEndpoint, setExpandedEndpoint] = useState<string | null>(null);
  const [testData, setTestData] = useState<Record<string, string>>({});

  const allTags = ['all', ...Array.from(new Set(API_ENDPOINTS.flatMap(endpoint => endpoint.tags)))];

  const filteredEndpoints = API_ENDPOINTS.filter(endpoint => {
    const matchesSearch = endpoint.path.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         endpoint.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         endpoint.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTag = selectedTag === 'all' || endpoint.tags.includes(selectedTag);
    
    return matchesSearch && matchesTag;
  });

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copiado para a área de transferência!');
  };

  const testEndpoint = async (endpoint: APIEndpoint) => {
    const endpointKey = `${endpoint.method}-${endpoint.path}`;
    const requestBody = testData[endpointKey] || endpoint.requestBody?.example || '';
    
    try {
      toast.loading('Testando endpoint...', { id: 'test-endpoint' });
      
      // Simular chamada da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Teste realizado com sucesso!', { 
        id: 'test-endpoint',
        description: 'Verifique a resposta abaixo'
      });
    } catch (error) {
      toast.error('Erro no teste do endpoint', { id: 'test-endpoint' });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Documentação da API</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Explore e teste todos os endpoints da API do Excel Copilot
        </p>
        
        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Pesquisar endpoints..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2 flex-wrap">
            {allTags.map(tag => (
              <Button
                key={tag}
                variant={selectedTag === tag ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTag(tag)}
              >
                {tag === 'all' ? 'Todos' : tag}
              </Button>
            ))}
          </div>
        </div>

        {/* API Info */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Base URL</h3>
                <code className="text-sm bg-muted p-2 rounded block">
                  https://excel-copilot-eight.vercel.app
                </code>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Autenticação</h3>
                <p className="text-sm text-muted-foreground">
                  Bearer Token via header Authorization
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Formato</h3>
                <p className="text-sm text-muted-foreground">
                  JSON (application/json)
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Endpoints */}
      <div className="space-y-4">
        {filteredEndpoints.map((endpoint, index) => {
          const endpointKey = `${endpoint.method}-${endpoint.path}`;
          const isExpanded = expandedEndpoint === endpointKey;
          
          return (
            <Card key={index} className="overflow-hidden">
              <CardHeader 
                className="cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => setExpandedEndpoint(isExpanded ? null : endpointKey)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Badge className={`${METHOD_COLORS[endpoint.method as keyof typeof METHOD_COLORS]} border`}>
                      {endpoint.method}
                    </Badge>
                    <div>
                      <CardTitle className="text-lg">{endpoint.path}</CardTitle>
                      <CardDescription>{endpoint.summary}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {endpoint.requiresAuth && (
                      <Lock className="h-4 w-4 text-amber-500" title="Requer autenticação" />
                    )}
                    {endpoint.adminOnly && (
                      <AlertCircle className="h-4 w-4 text-red-500" title="Apenas administradores" />
                    )}
                    {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </div>
                </div>
              </CardHeader>
              
              {isExpanded && (
                <CardContent className="border-t">
                  <Tabs defaultValue="overview" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                      <TabsTrigger value="parameters">Parâmetros</TabsTrigger>
                      <TabsTrigger value="responses">Respostas</TabsTrigger>
                      <TabsTrigger value="test">Testar</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Descrição</h4>
                        <p className="text-muted-foreground">{endpoint.description}</p>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2">Tags</h4>
                        <div className="flex gap-2">
                          {endpoint.tags.map(tag => (
                            <Badge key={tag} variant="outline">{tag}</Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2">Autenticação</h4>
                        <div className="flex items-center gap-2">
                          {endpoint.requiresAuth ? (
                            <Badge variant="destructive">
                              <Lock className="h-3 w-3 mr-1" />
                              Requer autenticação
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              <Globe className="h-3 w-3 mr-1" />
                              Público
                            </Badge>
                          )}
                          {endpoint.adminOnly && (
                            <Badge variant="destructive">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              Apenas administradores
                            </Badge>
                          )}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="parameters" className="space-y-4">
                      {endpoint.parameters && endpoint.parameters.length > 0 ? (
                        <div className="space-y-3">
                          {endpoint.parameters.map((param, paramIndex) => (
                            <div key={paramIndex} className="border rounded p-3">
                              <div className="flex items-center gap-2 mb-2">
                                <code className="text-sm font-mono">{param.name}</code>
                                <Badge variant={param.required ? 'destructive' : 'secondary'} className="text-xs">
                                  {param.required ? 'Obrigatório' : 'Opcional'}
                                </Badge>
                                <Badge variant="outline" className="text-xs">{param.type}</Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{param.description}</p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-muted-foreground">Nenhum parâmetro necessário</p>
                      )}

                      {endpoint.requestBody && (
                        <div>
                          <h4 className="font-semibold mb-2">Corpo da Requisição</h4>
                          <div className="relative">
                            <pre className="bg-muted p-4 rounded text-sm overflow-x-auto">
                              <code>{endpoint.requestBody.example}</code>
                            </pre>
                            <Button
                              size="sm"
                              variant="outline"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(endpoint.requestBody!.example)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="responses" className="space-y-4">
                      {endpoint.responses?.map((response, responseIndex) => (
                        <div key={responseIndex} className="border rounded p-4">
                          <div className="flex items-center gap-2 mb-3">
                            <Badge 
                              variant={response.status < 300 ? 'default' : 'destructive'}
                              className="text-sm"
                            >
                              {response.status}
                            </Badge>
                            <span className="font-medium">{response.description}</span>
                          </div>
                          <div className="relative">
                            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                              <code>{response.example}</code>
                            </pre>
                            <Button
                              size="sm"
                              variant="outline"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(response.example)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </TabsContent>

                    <TabsContent value="test" className="space-y-4">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="test-url">URL do Endpoint</Label>
                          <Input
                            id="test-url"
                            value={`https://excel-copilot-eight.vercel.app${endpoint.path}`}
                            readOnly
                            className="font-mono text-sm"
                          />
                        </div>

                        {endpoint.requestBody && (
                          <div>
                            <Label htmlFor="test-body">Corpo da Requisição (JSON)</Label>
                            <Textarea
                              id="test-body"
                              value={testData[endpointKey] || endpoint.requestBody.example}
                              onChange={(e) => setTestData(prev => ({ ...prev, [endpointKey]: e.target.value }))}
                              className="font-mono text-sm min-h-[200px]"
                            />
                          </div>
                        )}

                        <Button onClick={() => testEndpoint(endpoint)} className="w-full">
                          <Play className="h-4 w-4 mr-2" />
                          Testar Endpoint
                        </Button>

                        <div className="text-sm text-muted-foreground">
                          <p>💡 <strong>Dica:</strong> Para testar endpoints autenticados, certifique-se de estar logado na aplicação.</p>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {filteredEndpoints.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Nenhum endpoint encontrado com os filtros aplicados.</p>
        </div>
      )}
    </div>
  );
}
